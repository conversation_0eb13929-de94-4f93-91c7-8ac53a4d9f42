import { z } from 'zod'
import { ProductStatus, PaymentGateway, PlanInterval } from './types'

export const productSchema = z.object({
  planId: z.string().min(1, 'Plan ID is required'),
  providerName: z.nativeEnum(PaymentGateway, {
    errorMap: () => ({ message: 'Please select a valid payment gateway' })
  }),
  name: z.string().min(1, 'Product name is required'),
  description: z.string().min(1, 'Description is required'),
  price: z.number().min(0, 'Price must be a positive number'),
  credits: z.number().min(0, 'Credits must be a positive number'),
  status: z.nativeEnum(ProductStatus, {
    errorMap: () => ({ message: 'Please select a valid status' })
  }),
  interval: z.nativeEnum(PlanInterval, {
    errorMap: () => ({ message: 'Please select a valid interval' })
  }),
  featureIds: z.array(z.string()).default([])
})

export const featureSchema = z.object({
  name: z.string().min(1, 'Feature name is required').max(100, 'Feature name is too long')
})

export type ProductFormData = z.infer<typeof productSchema>
export type FeatureFormData = z.infer<typeof featureSchema>