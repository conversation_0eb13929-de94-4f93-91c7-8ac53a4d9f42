import CryptoJS from 'crypto-js';
import Imap from 'imap';
import { J<PERSON><PERSON> } from 'jsdom';
import { simpleParser } from 'mailparser';
import puppeteer, { <PERSON><PERSON> } from 'puppeteer';
import { Readable } from 'stream';
import fs from 'fs/promises';
import path from 'path';

const CREEM_API_KEY = process.env.CREEM_API_KEY;
const CREEM_API_URL =
  process.env.CREEM_TEST_MODE === 'true'
    ? 'https://test-api.creem.io/v1'
    : 'https://api.creem.io/v1';
const EMAIL_USER = process.env.CREEM_EMAIL;
const EMAIL_PASSWORD = process.env.CREEM_GOOGLE_APP_PASSWORD;
const ENCRYPTION_KEY =
  process.env.ENCRYPTION_KEY || 'default-key-change-in-production';

process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';

// Path to store the session file
const SESSION_FILE_PATH = path.join(process.cwd(), 'creem-session.json');

// Simple encryption/decryption functions
function encrypt(text: string): string {
  return CryptoJS.AES.encrypt(text, ENCRYPTION_KEY).toString();
}

function decrypt(ciphertext: string): string {
  const bytes = CryptoJS.AES.decrypt(ciphertext, ENCRYPTION_KEY);
  return bytes.toString(CryptoJS.enc.Utf8);
}

async function getCreemSession(): Promise<Cookie[]> {
  console.log('Getting creem session');

  try {
    // Try to read session from JSON file
    const sessionData = await fs.readFile(SESSION_FILE_PATH, 'utf-8');
    const sessionObj = JSON.parse(sessionData);

    if (sessionObj.encryptedCookies && sessionObj.timestamp) {
      // Check if session is not too old (24 hours)
      const sessionAge = Date.now() - sessionObj.timestamp;
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

      if (sessionAge < maxAge) {
        try {
          const decryptedValue = decrypt(sessionObj.encryptedCookies);
          const cookies = JSON.parse(decryptedValue);

          // Check if session is still valid
          const isValid = await validateCreemSession(cookies);
          if (isValid) {
            console.log('Using existing valid session');
            return cookies;
          }
        } catch (e) {
          console.log('Invalid session data, getting new one');
        }
      } else {
        console.log('Session expired, getting new one');
      }
    }
  } catch (e) {
    console.log('No session file found or error reading it, creating new one');
  }

  // Get new session if not valid or doesn't exist
  return await refreshCreemSession();
}

async function validateCreemSession(cookies: Cookie[]): Promise<boolean> {
  try {
    const response = await fetch(`${CREEM_API_URL}/products`, {
      headers: {
        Cookie: cookies.map((c) => `${c.name}=${c.value}`).join('; '),
      },
    });
    return response.ok;
  } catch (e) {
    return false;
  }
}

async function refreshCreemSession(): Promise<Cookie[]> {
  console.log('Generating new session');

  // First set up IMAP connection and prepare to receive the email
  const loginLinkPromise = getCreemLoginLinkFromEmail();

  // Wait a moment to ensure IMAP is connected before requesting the email
  await new Promise((resolve) => setTimeout(resolve, 2000));

  // Then request magic link email
  await requestCreemMagicLink();

  // Wait for the login link from email
  const loginLink = await loginLinkPromise;

  // Use puppeteer to login and get cookies
  const cookies = await loginWithPuppeteer(loginLink);

  // Encrypt and save cookies to JSON file
  const encryptedValue = encrypt(JSON.stringify(cookies));
  const sessionData = {
    encryptedCookies: encryptedValue,
    timestamp: Date.now(),
    createdAt: new Date().toISOString(),
  };

  try {
    await fs.writeFile(SESSION_FILE_PATH, JSON.stringify(sessionData, null, 2));
    console.log('Session saved to file successfully');
  } catch (e) {
    console.log('Could not save session to file:', e);
  }

  return cookies;
}

async function requestCreemMagicLink(): Promise<void> {
  console.log('Requesting magic link email');

  const csrfRes = await fetch('https://www.creem.io/api/auth/csrf');
  const { csrfToken } = await csrfRes.json();

  const params = new URLSearchParams();
  params.append('email', EMAIL_USER as string);
  params.append('csrfToken', csrfToken);
  params.append('callbackUrl', 'https://www.creem.io/sign-in');

  console.log(csrfToken);

  const result = await fetch('https://www.creem.io/api/auth/signin/resend', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: params.toString(),
  }).then((res) => res.text());

  if (result.includes('A sign in link has been sent you email.')) {
    console.log('Email has been successfully sent');
  }
  console.log(result);
}

async function getCreemLoginLinkFromEmail(): Promise<string> {
  return new Promise((resolve, reject) => {
    const imap = new Imap({
      user: EMAIL_USER as string,
      password: EMAIL_PASSWORD as string,
      host: 'imap.gmail.com',
      port: 993,
      tls: true,
    });

    const timeout = setTimeout(() => {
      reject(new Error('Timeout waiting for email'));
    }, 120_000);

    imap.once('ready', () => {
      imap.openBox('INBOX', false, (err) => {
        if (err) reject(err);

        const checkEmail = () => {
          const fetch = imap.seq.fetch('1:*', { bodies: '' });

          fetch.on('message', (msg) => {
            msg.on('body', (stream) => {
              simpleParser(Readable.from(stream), (err, parsed) => {
                const from = parsed.from?.text ?? '';

                if (from.toLowerCase().includes('creem')) {
                  const body = parsed.html || parsed.text || '';
                  const dom = new JSDOM(body);
                  const link = [
                    ...dom.window.document.querySelectorAll('a'),
                  ].find((a) => a.href.includes('api/auth/callback/resend'));

                  if (link) {
                    clearTimeout(timeout);
                    imap.end();
                    resolve(link.href);
                  }
                }
              });
            });
          });
        };

        imap.on('mail', checkEmail);
        checkEmail(); // Check existing emails
      });
    });

    imap.once('error', reject);
    imap.connect();
  });
}

async function loginWithPuppeteer(loginLink: string): Promise<Cookie[]> {
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();

  await page.goto(loginLink, { waitUntil: 'networkidle0' });
  const cookies = await page.cookies();

  await browser.close();
  return cookies;
}

async function fetchCreem(
  method: 'POST' | 'PATCH' | 'DELETE' | 'PUT' | 'GET',
  url: string,
  body?: any,
  useSession = false
) {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  if (useSession) {
    const cookies = await getCreemSession();
    headers.Cookie = cookies
      .map((c: Cookie) => `${c.name}=${c.value}`)
      .join('; ');
  } else {
    headers['x-api-key'] = CREEM_API_KEY ?? '';
  }

  const res = await fetch(url, {
    method,
    headers,
    body: body ? JSON.stringify(body) : undefined,
  });

  if (!res.ok) {
    const errorText = await res.text();
    console.log('Creem API Error:', errorText);
    throw new Error(`Creem API error: ${res.statusText}`);
  }

  return res.json();
}

// Map our interval enum to Creem's billing period format
function mapIntervalToCreemBillingPeriod(interval: string): string {
  const mapping: Record<string, string> = {
    DAILY: 'every-day',
    WEEKLY: 'every-week',
    MONTHLY: 'every-month',
    QUARTERLY: 'every-3-months',
    YEARLY: 'every-year',
  };
  return mapping[interval] || 'every-month';
}

export const creem = {
  async createProduct(productData: {
    name: string;
    description: string;
    price: number;
    interval: string;
  }) {
    try {
      console.log('Creating product in Creem:', productData);

      const creemProduct = await fetchCreem(
        'POST',
        `${CREEM_API_URL}/products`,
        {
          name: productData.name,
          description: productData.description,
          price: productData.price,
          billing_type: 'recurring',
          currency: 'USD',
          billing_period: mapIntervalToCreemBillingPeriod(productData.interval),
        }
      );

      console.log('Creem product created:', creemProduct);
      return creemProduct;
    } catch (error) {
      console.error('Failed to create product in Creem:', error);
      throw error;
    }
  },

  async updateProduct(
    planId: string,
    productData: {
      name: string;
      description: string;
      price: number;
    }
  ) {
    try {
      console.log('Updating product in Creem:', planId, productData);

      const result = await fetchCreem(
        'PUT',
        `${CREEM_API_URL}/products/edit?product_id=${planId}`,
        {
          name: productData.name,
          description: productData.description,
          price: productData.price,
        }
      );

      console.log('Creem product updated:', result);
      return result;
    } catch (error) {
      console.error('Failed to update product in Creem:', error);
      throw error;
    }
  },

  async archiveProduct(planId: string) {
    try {
      console.log('Archiving product in Creem:', planId);

      const result = await fetchCreem(
        'PUT',
        `https://www.creem.io/api/products/archive?product_id=${planId}`,
        { archived: true },
        true
      );

      console.log('Creem product archived:', result);
      return result;
    } catch (error) {
      console.error('Failed to archive product in Creem:', error);
      throw error;
    }
  },

  async getProducts() {
    try {
      const products = await fetchCreem(
        'GET',
        `${CREEM_API_URL}/products`,
        null,
        true
      );
      return products;
    } catch (error) {
      console.error('Failed to get products from Creem:', error);
      throw error;
    }
  },

  // Utility function to clear the session file (useful for debugging)
  async clearSession() {
    try {
      await fs.unlink(SESSION_FILE_PATH);
      console.log('Session file cleared');
    } catch (e) {
      console.log('No session file to clear or error clearing it');
    }
  },

  // Utility function to get session info
  async getSessionInfo() {
    try {
      const sessionData = await fs.readFile(SESSION_FILE_PATH, 'utf-8');
      const sessionObj = JSON.parse(sessionData);

      return {
        exists: true,
        createdAt: sessionObj.createdAt,
        timestamp: sessionObj.timestamp,
        age: Date.now() - sessionObj.timestamp,
        isExpired: Date.now() - sessionObj.timestamp > 24 * 60 * 60 * 1000,
      };
    } catch (e) {
      return {
        exists: false,
        createdAt: null,
        timestamp: null,
        age: null,
        isExpired: true,
      };
    }
  },
};
