export enum ProductStatus {
  active = 'active',
  archived = 'archived'
}

export enum PaymentGateway {
  GUMROAD = 'GUMROAD',
  LEMONSQUEEZY = 'LEMONSQUEEZY',
  PADDLE = 'PADDLE',
  CREEM = 'CREEM'
}

export enum SubscriptionStatus {
  TRIALING = 'TRIALING',
  ACTIVE = 'ACTIVE',
  PAUSED = 'PAUSED',
  PAST_DUE = 'PAST_DUE',
  UNPAID = 'UNPAID',
  CANCELLED = 'CANCELLED',
  EXPIRED = 'EXPIRED',
  REFUNDED = 'REFUNDED'
}

export enum PlanInterval {
  DAILY = 'DAILY',
  WEEKLY = 'WEEKLY',
  MONTHLY = 'MONTHLY',
  QUARTERLY = 'QUARTERLY',
  YEARLY = 'YEARLY'
}

export enum PaymentStatus {
  PENDING = 'PENDING',
  SUCCEEDED = 'SUCCEEDED',
  FAILED = 'FAILED',
  REFUNDED = 'REFUNDED'
}

export enum PaymentMethod {
  CREDIT_CARD = 'CREDIT_CARD',
  PAYPAL = 'PAYPAL',
  APPLE_PAY = 'APPLE_PAY',
  GOOGLE_PAY = 'GOOGLE_PAY',
  BANK_TRANSFER = 'BANK_TRANSFER'
}

export interface Product {
  id: string
  planId: string
  providerName: PaymentGateway
  name: string
  description: string
  price: number
  credits: number
  status: ProductStatus
  interval: PlanInterval
  order: number
  createdAt: Date
  updatedAt: Date
  features?: Feature[]
}

export interface Feature {
  id: string
  name: string
  createdAt: Date
  updatedAt: Date
  products?: Product[]
}

export interface ProductFormData {
  planId: string
  providerName: PaymentGateway
  name: string
  description: string
  price: number
  credits: number
  status: ProductStatus
  interval: PlanInterval
  featureIds: string[]
}

export interface FeatureFormData {
  name: string
}