'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { RefreshCw, CheckCircle, XCircle, Trash2, Info } from 'lucide-react';

export default function SettingsPage() {
  const [isTestingCreem, setIsTestingCreem] = useState(false);
  const [isClearingSession, setIsClearingSession] = useState(false);
  const [creemStatus, setCreemStatus] = useState<
    'unknown' | 'connected' | 'error'
  >('unknown');
  const [sessionInfo, setSessionInfo] = useState<any>(null);

  const testCreemConnection = async () => {
    setIsTestingCreem(true);
    try {
      const response = await fetch('/api/creem/test', {
        method: 'POST',
      });

      if (response.ok) {
        setCreemStatus('connected');
        toast.success('Creem.io connection successful');
      } else {
        setCreemStatus('error');
        toast.error('Failed to connect to Creem.io');
      }
    } catch (error) {
      setCreemStatus('error');
      toast.error('Failed to test Creem.io connection');
    } finally {
      setIsTestingCreem(false);
    }
  };

  const clearCreemSession = async () => {
    setIsClearingSession(true);
    try {
      const response = await fetch('/api/creem/clear-session', {
        method: 'POST',
      });

      if (response.ok) {
        toast.success('Creem.io session cleared successfully');
        setSessionInfo(null);
        setCreemStatus('unknown');
      } else {
        toast.error('Failed to clear Creem.io session');
      }
    } catch (error) {
      toast.error('Failed to clear Creem.io session');
    } finally {
      setIsClearingSession(false);
    }
  };

  const getSessionInfo = async () => {
    try {
      const response = await fetch('/api/creem/session-info');
      if (response.ok) {
        const info = await response.json();
        setSessionInfo(info);
      }
    } catch (error) {
      console.error('Failed to get session info:', error);
    }
  };

  const getStatusBadge = () => {
    switch (creemStatus) {
      case 'connected':
        return (
          <Badge className="bg-green-100 text-green-800">
            <CheckCircle className="w-3 h-3 mr-1" />
            Connected
          </Badge>
        );
      case 'error':
        return (
          <Badge variant="destructive">
            <XCircle className="w-3 h-3 mr-1" />
            Error
          </Badge>
        );
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
        <p className="mt-2 text-gray-600">
          Application configuration and integrations
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Database Configuration</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Database URL
            </label>
            <p className="mt-1 text-sm text-gray-500">
              Configure your database connection in the .env.local file
            </p>
            <code className="mt-2 block p-3 bg-gray-100 rounded text-sm">
              DATABASE_URL=your_database_url_here
            </code>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Creem.io Integration</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-gray-900">
                Connection Status
              </h4>
              <p className="text-sm text-gray-500">
                Test your Creem.io API connection
              </p>
            </div>
            <div className="flex items-center space-x-2">
              {getStatusBadge()}
              <Button
                onClick={testCreemConnection}
                disabled={isTestingCreem}
                size="sm"
              >
                {isTestingCreem ? (
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <RefreshCw className="w-4 h-4 mr-2" />
                )}
                Test Connection
              </Button>
            </div>
          </div>

          <div className="flex items-center justify-between border-t pt-4">
            <div>
              <h4 className="text-sm font-medium text-gray-900">
                Session Management
              </h4>
              <p className="text-sm text-gray-500">
                Manage Creem.io authentication session
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Button onClick={getSessionInfo} variant="outline" size="sm">
                <Info className="w-4 h-4 mr-2" />
                Session Info
              </Button>
              <Button
                onClick={clearCreemSession}
                disabled={isClearingSession}
                variant="outline"
                size="sm"
              >
                {isClearingSession ? (
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Trash2 className="w-4 h-4 mr-2" />
                )}
                Clear Session
              </Button>
            </div>
          </div>

          {sessionInfo && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h5 className="text-sm font-medium text-gray-900 mb-2">
                Session Information
              </h5>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Status:</span>
                  <span
                    className={
                      sessionInfo.exists ? 'text-green-600' : 'text-red-600'
                    }
                  >
                    {sessionInfo.exists ? 'File exists' : 'No session file'}
                  </span>
                </div>
                {sessionInfo.exists && (
                  <>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Created:</span>
                      <span className="text-gray-900">
                        {sessionInfo.createdAt}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Age:</span>
                      <span className="text-gray-900">
                        {Math.round(sessionInfo.age / (1000 * 60))} minutes
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Expired:</span>
                      <span
                        className={
                          sessionInfo.isExpired
                            ? 'text-red-600'
                            : 'text-green-600'
                        }
                      >
                        {sessionInfo.isExpired ? 'Yes' : 'No'}
                      </span>
                    </div>
                  </>
                )}
              </div>
            </div>
          )}

          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Required Environment Variables
              </label>
              <div className="mt-2 space-y-2">
                <code className="block p-2 bg-gray-100 rounded text-xs">
                  CREEM_API_KEY=your_creem_api_key_here
                </code>
                <code className="block p-2 bg-gray-100 rounded text-xs">
                  CREEM_EMAIL=<EMAIL>
                </code>
                <code className="block p-2 bg-gray-100 rounded text-xs">
                  CREEM_GOOGLE_APP_PASSWORD=your_google_app_password_here
                </code>
                <code className="block p-2 bg-gray-100 rounded text-xs">
                  CREEM_TEST_MODE=false
                </code>
                <code className="block p-2 bg-gray-100 rounded text-xs">
                  ENCRYPTION_KEY=your-secret-encryption-key
                </code>
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h5 className="text-sm font-medium text-blue-900 mb-2">
                Integration Features
              </h5>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>
                  • Automatic product creation in Creem.io when provider is
                  CREEM
                </li>
                <li>• Product updates synchronized with Creem.io</li>
                <li>• Product archiving in Creem.io when deleted</li>
                <li>
                  • Session stored in encrypted JSON file (creem-session.json)
                </li>
                <li>• Automatic session refresh when expired</li>
              </ul>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h5 className="text-sm font-medium text-yellow-900 mb-2">
                Session Storage
              </h5>
              <p className="text-sm text-yellow-800">
                Creem.io sessions are now stored in an encrypted JSON file
                (creem-session.json) in your project root. Sessions are
                automatically refreshed when expired (24 hours). You can clear
                the session manually if needed.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Application Info</CardTitle>
        </CardHeader>
        <CardContent>
          <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
            <div>
              <dt className="text-sm font-medium text-gray-500">Version</dt>
              <dd className="mt-1 text-sm text-gray-900">1.0.0</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Environment</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {process.env.NODE_ENV === 'production'
                  ? 'Production'
                  : 'Development'}
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Creem Mode</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {process.env.CREEM_TEST_MODE === 'true' ? 'Test' : 'Production'}
              </dd>
            </div>
          </dl>
        </CardContent>
      </Card>
    </div>
  );
}
