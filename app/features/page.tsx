'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { DataTable } from '@/components/ui/data-table'
import { Feature } from '@/lib/types'
import { FeatureForm } from '@/components/features/feature-form'
import { FeatureFormData } from '@/lib/validations'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Plus } from 'lucide-react'
import { toast } from 'sonner'

export default function FeaturesPage() {
  const [features, setFeatures] = useState<Feature[]>([])
  const [loading, setLoading] = useState(true)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingFeature, setEditingFeature] = useState<Feature | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    fetchFeatures()
  }, [])

  const fetchFeatures = async () => {
    try {
      const response = await fetch('/api/features')
      if (!response.ok) throw new Error('Failed to fetch features')
      const data = await response.json()
      setFeatures(data)
    } catch (error) {
      toast.error('Failed to load features')
      console.error('Error fetching features:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCreate = () => {
    setEditingFeature(null)
    setIsDialogOpen(true)
  }

  const handleEdit = (feature: Feature) => {
    setEditingFeature(feature)
    setIsDialogOpen(true)
  }

  const handleDelete = async (feature: Feature) => {
    if (!confirm(`Are you sure you want to delete "${feature.name}"?`)) {
      return
    }

    try {
      const response = await fetch(`/api/features/${feature.id}`, {
        method: 'DELETE'
      })
      
      if (!response.ok) throw new Error('Failed to delete feature')
      
      toast.success('Feature deleted successfully')
      fetchFeatures()
    } catch (error) {
      toast.error('Failed to delete feature')
      console.error('Error deleting feature:', error)
    }
  }

  const handleSubmit = async (data: FeatureFormData) => {
    setIsSubmitting(true)
    try {
      const url = editingFeature 
        ? `/api/features/${editingFeature.id}`
        : '/api/features'
      
      const method = editingFeature ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) throw new Error(`Failed to ${editingFeature ? 'update' : 'create'} feature`)

      toast.success(`Feature ${editingFeature ? 'updated' : 'created'} successfully`)
      setIsDialogOpen(false)
      setEditingFeature(null)
      fetchFeatures()
    } catch (error) {
      toast.error(`Failed to ${editingFeature ? 'update' : 'create'} feature`)
      console.error('Error saving feature:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCancel = () => {
    setIsDialogOpen(false)
    setEditingFeature(null)
  }

  const columns = [
    {
      key: 'name',
      label: 'Feature Name'
    },
    {
      key: 'products',
      label: 'Used in Products',
      render: (products: any[]) => (
        <div className="text-sm text-gray-600">
          {products?.length || 0} product{(products?.length || 0) !== 1 ? 's' : ''}
        </div>
      )
    },
    {
      key: 'createdAt',
      label: 'Created',
      render: (value: string) => new Date(value).toLocaleDateString()
    }
  ]

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading features...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Features</h1>
          <p className="mt-2 text-gray-600">
            Manage product features
          </p>
        </div>
        <Button onClick={handleCreate}>
          <Plus className="mr-2 h-4 w-4" />
          Add Feature
        </Button>
      </div>

      <div className="bg-white rounded-lg shadow">
        <DataTable
          data={features}
          columns={columns}
          onEdit={handleEdit}
          onDelete={handleDelete}
        />
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>
              {editingFeature ? 'Edit Feature' : 'Create Feature'}
            </DialogTitle>
          </DialogHeader>
          <FeatureForm
            feature={editingFeature || undefined}
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            isSubmitting={isSubmitting}
          />
        </DialogContent>
      </Dialog>
    </div>
  )
}