'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { ProductForm } from '@/components/products/product-form'
import { Product, Feature } from '@/lib/types'
import { ProductFormData } from '@/lib/validations'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { toast } from 'sonner'

interface EditProductPageProps {
  params: {
    id: string
  }
}

export default function EditProductPage({ params }: EditProductPageProps) {
  const [product, setProduct] = useState<Product | null>(null)
  const [features, setFeatures] = useState<Feature[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    fetchData()
  }, [params.id])

  const fetchData = async () => {
    try {
      const [productResponse, featuresResponse] = await Promise.all([
        fetch(`/api/products/${params.id}`),
        fetch('/api/features')
      ])

      if (!productResponse.ok) throw new Error('Failed to fetch product')
      if (!featuresResponse.ok) throw new Error('Failed to fetch features')

      const [productData, featuresData] = await Promise.all([
        productResponse.json(),
        featuresResponse.json()
      ])

      setProduct(productData)
      setFeatures(featuresData)
    } catch (error) {
      toast.error('Failed to load product data')
      console.error('Error fetching data:', error)
      router.push('/products')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (data: ProductFormData) => {
    setIsSubmitting(true)
    try {
      const response = await fetch(`/api/products/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) throw new Error('Failed to update product')

      toast.success('Product updated successfully')
      router.push('/products')
    } catch (error) {
      toast.error('Failed to update product')
      console.error('Error updating product:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCancel = () => {
    router.push('/products')
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading product...</div>
      </div>
    )
  }

  if (!product) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Product not found</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Edit Product</h1>
        <p className="mt-2 text-gray-600">
          Update product information
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Product Details</CardTitle>
        </CardHeader>
        <CardContent>
          <ProductForm
            product={product}
            features={features}
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            isSubmitting={isSubmitting}
          />
        </CardContent>
      </Card>
    </div>
  )
}