import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Package, Star, TrendingUp, DollarSign } from 'lucide-react'
import { db } from '@/lib/db'

async function getDashboardStats() {
  try {
    const [products, features] = await Promise.all([
      db.getProducts(),
      db.getFeatures()
    ])

    const activeProducts = products.filter(p => p.status === 'active').length
    const totalRevenue = products.reduce((sum, p) => sum + p.price, 0)

    return {
      totalProducts: products.length,
      activeProducts,
      totalFeatures: features.length,
      totalRevenue
    }
  } catch (error) {
    console.error('Error fetching dashboard stats:', error)
    return {
      totalProducts: 0,
      activeProducts: 0,
      totalFeatures: 0,
      totalRevenue: 0
    }
  }
}

export default async function Dashboard() {
  const stats = await getDashboardStats()

  const cards = [
    {
      title: 'Total Products',
      value: stats.totalProducts,
      description: `${stats.activeProducts} active`,
      icon: Package,
      color: 'text-blue-600'
    },
    {
      title: 'Features',
      value: stats.totalFeatures,
      description: 'Available features',
      icon: Star,
      color: 'text-yellow-600'
    },
    {
      title: 'Active Products',
      value: stats.activeProducts,
      description: 'Currently active',
      icon: TrendingUp,
      color: 'text-green-600'
    },
    {
      title: 'Total Revenue',
      value: `$${(stats.totalRevenue / 100).toFixed(2)}`,
      description: 'Combined pricing',
      icon: DollarSign,
      color: 'text-purple-600'
    }
  ]

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="mt-2 text-gray-600">
          Welcome to your product management dashboard
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {cards.map((card) => (
          <Card key={card.title} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {card.title}
              </CardTitle>
              <card.icon className={`h-4 w-4 ${card.color}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">
                {card.value}
              </div>
              <p className="text-xs text-gray-500 mt-1">
                {card.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Get started with common tasks
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a
              href="/products/new"
              className="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Package className="h-6 w-6 text-blue-600 mb-2" />
              <h3 className="font-medium text-gray-900">Create Product</h3>
              <p className="text-sm text-gray-500">Add a new product to your catalog</p>
            </a>
            
            <a
              href="/features"
              className="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Star className="h-6 w-6 text-yellow-600 mb-2" />
              <h3 className="font-medium text-gray-900">Manage Features</h3>
              <p className="text-sm text-gray-500">Create and edit product features</p>
            </a>
            
            <a
              href="/products"
              className="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <TrendingUp className="h-6 w-6 text-green-600 mb-2" />
              <h3 className="font-medium text-gray-900">View Products</h3>
              <p className="text-sm text-gray-500">Browse and manage existing products</p>
            </a>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}