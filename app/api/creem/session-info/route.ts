import { NextResponse } from 'next/server';
import { creem } from '@/lib/creem';

export async function GET() {
  try {
    const sessionInfo = await creem.getSessionInfo();

    return NextResponse.json(sessionInfo);
  } catch (error) {
    console.error('Failed to get session info:', error);

    return NextResponse.json(
      {
        success: false,
        message: 'Failed to get session info',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
