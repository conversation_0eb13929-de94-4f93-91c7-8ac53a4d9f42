import { NextResponse } from 'next/server'
import { creem } from '@/lib/creem'

export async function POST() {
  try {
    // Test the Creem connection by fetching products
    await creem.getProducts()
    
    return NextResponse.json({ 
      success: true, 
      message: 'Creem.io connection successful' 
    })
  } catch (error) {
    console.error('Creem connection test failed:', error)
    
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to connect to Creem.io',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}