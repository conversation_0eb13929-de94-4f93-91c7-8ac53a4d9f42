import { NextResponse } from 'next/server';
import { creem } from '@/lib/creem';

export async function POST() {
  try {
    await creem.clearSession();

    return NextResponse.json({
      success: true,
      message: 'Creem.io session cleared successfully',
    });
  } catch (error) {
    console.error('Failed to clear Creem session:', error);

    return NextResponse.json(
      {
        success: false,
        message: 'Failed to clear Creem.io session',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
