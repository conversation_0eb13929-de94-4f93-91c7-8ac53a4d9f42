import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { featureSchema } from '@/lib/validations'

export async function GET() {
  try {
    const features = await db.getFeatures()
    return NextResponse.json(features)
  } catch (error) {
    console.error('Error fetching features:', error)
    return NextResponse.json(
      { error: 'Failed to fetch features' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = featureSchema.parse(body)
    
    const feature = await db.createFeature(validatedData)
    return NextResponse.json(feature, { status: 201 })
  } catch (error) {
    console.error('Error creating feature:', error)
    return NextResponse.json(
      { error: 'Failed to create feature' },
      { status: 500 }
    )
  }
}