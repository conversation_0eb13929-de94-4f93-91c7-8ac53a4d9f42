'use client'

import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { featureSchema, FeatureFormData } from '@/lib/validations'
import { Feature } from '@/lib/types'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'

interface FeatureFormProps {
  feature?: Feature
  onSubmit: (data: FeatureFormData) => Promise<void>
  onCancel: () => void
  isSubmitting?: boolean
}

export function FeatureForm({
  feature,
  onSubmit,
  onCancel,
  isSubmitting = false
}: FeatureFormProps) {
  const form = useForm<FeatureFormData>({
    resolver: zodResolver(featureSchema),
    defaultValues: {
      name: feature?.name || ''
    }
  })

  const handleSubmit = async (data: FeatureFormData) => {
    await onSubmit(data)
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Feature Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter feature name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? 'Saving...' : (feature ? 'Update Feature' : 'Create Feature')}
          </Button>
        </div>
      </form>
    </Form>
  )
}